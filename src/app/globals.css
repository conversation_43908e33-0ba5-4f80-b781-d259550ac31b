@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-roboto);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Ocean/Teal inspired palette with WCAG AA compliance */
  --background: oklch(0.99 0.005 200);
  --foreground: oklch(0.15 0.02 220);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 220);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 220);
  --primary: oklch(0.45 0.15 195); /* Deep teal */
  --primary-foreground: oklch(0.98 0.01 200);
  --secondary: oklch(0.92 0.03 200); /* Light ocean blue */
  --secondary-foreground: oklch(0.25 0.05 200);
  --muted: oklch(0.95 0.02 200);
  --muted-foreground: oklch(0.5 0.05 200);
  --accent: oklch(0.65 0.12 180); /* Vibrant teal accent */
  --accent-foreground: oklch(0.98 0.01 200);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 200);
  --input: oklch(0.95 0.02 200);
  --ring: oklch(0.45 0.15 195);
  --chart-1: oklch(0.55 0.15 195);
  --chart-2: oklch(0.6 0.12 180);
  --chart-3: oklch(0.65 0.1 165);
  --chart-4: oklch(0.7 0.08 150);
  --chart-5: oklch(0.75 0.06 135);
  --sidebar: oklch(0.98 0.01 200);
  --sidebar-foreground: oklch(0.15 0.02 220);
  --sidebar-primary: oklch(0.45 0.15 195);
  --sidebar-primary-foreground: oklch(0.98 0.01 200);
  --sidebar-accent: oklch(0.92 0.03 200);
  --sidebar-accent-foreground: oklch(0.25 0.05 200);
  --sidebar-border: oklch(0.9 0.02 200);
  --sidebar-ring: oklch(0.45 0.15 195);
}

.dark {
  /* Dark ocean theme */
  --background: oklch(0.08 0.02 220);
  --foreground: oklch(0.95 0.01 200);
  --card: oklch(0.12 0.03 220);
  --card-foreground: oklch(0.95 0.01 200);
  --popover: oklch(0.12 0.03 220);
  --popover-foreground: oklch(0.95 0.01 200);
  --primary: oklch(0.65 0.12 180); /* Bright teal for dark mode */
  --primary-foreground: oklch(0.08 0.02 220);
  --secondary: oklch(0.18 0.04 210);
  --secondary-foreground: oklch(0.9 0.02 200);
  --muted: oklch(0.15 0.03 215);
  --muted-foreground: oklch(0.7 0.03 200);
  --accent: oklch(0.55 0.15 195);
  --accent-foreground: oklch(0.95 0.01 200);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.2 0.04 210);
  --input: oklch(0.18 0.04 210);
  --ring: oklch(0.65 0.12 180);
  --chart-1: oklch(0.55 0.15 195);
  --chart-2: oklch(0.6 0.12 180);
  --chart-3: oklch(0.65 0.1 165);
  --chart-4: oklch(0.7 0.08 150);
  --chart-5: oklch(0.75 0.06 135);
  --sidebar: oklch(0.12 0.03 220);
  --sidebar-foreground: oklch(0.95 0.01 200);
  --sidebar-primary: oklch(0.65 0.12 180);
  --sidebar-primary-foreground: oklch(0.08 0.02 220);
  --sidebar-accent: oklch(0.18 0.04 210);
  --sidebar-accent-foreground: oklch(0.9 0.02 200);
  --sidebar-border: oklch(0.2 0.04 210);
  --sidebar-ring: oklch(0.65 0.12 180);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-roboto), system-ui, sans-serif;
  }
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  /* 8pt spacing system */
  .space-8 { @apply space-y-8; }
  .space-16 { @apply space-y-16; }
  .space-24 { @apply space-y-24; }
  .space-32 { @apply space-y-32; }

  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Gradient backgrounds */
  .bg-ocean-gradient {
    background: linear-gradient(135deg, oklch(0.65 0.12 180) 0%, oklch(0.45 0.15 195) 100%);
  }

  .bg-ocean-gradient-light {
    background: linear-gradient(135deg, oklch(0.92 0.03 200) 0%, oklch(0.95 0.02 200) 100%);
  }

  /* Text gradients */
  .text-ocean-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-float,
  .animate-pulse-slow {
    animation: none;
  }

  html {
    scroll-behavior: auto;
  }
}
