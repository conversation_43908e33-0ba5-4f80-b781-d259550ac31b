'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, TrendingUp, Clock, DollarSign, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const CaseStudies = () => {
  const [currentCase, setCurrentCase] = useState(0);

  const caseStudies = [
    {
      company: 'TechCorp Solutions',
      industry: 'Technology',
      challenge: 'Manual invoice processing taking 40+ hours per week',
      solution: 'Implemented automated invoice processing with AI validation',
      results: [
        { icon: Clock, label: 'Time Saved', value: '95%', description: 'Reduced from 40 to 2 hours/week' },
        { icon: DollarSign, label: 'Cost Reduction', value: '$120K', description: 'Annual savings achieved' },
        { icon: TrendingUp, label: 'Accuracy Improved', value: '99.9%', description: 'Error rate reduced to 0.1%' },
      ],
      quote: "<PERSON><PERSON><PERSON><PERSON> transformed our financial operations completely. What used to take our team days now happens automatically with incredible accuracy.",
      author: "<PERSON>",
      role: "CFO, TechCorp Solutions",
      image: '/api/placeholder/400/300'
    },
    {
      company: 'Global Manufacturing Inc',
      industry: 'Manufacturing',
      challenge: 'Complex multi-currency reconciliation processes',
      solution: 'Deployed AI-driven reconciliation with real-time processing',
      results: [
        { icon: Clock, label: 'Processing Speed', value: '10x', description: 'Faster reconciliation cycles' },
        { icon: Users, label: 'Team Efficiency', value: '300%', description: 'Productivity increase' },
        { icon: TrendingUp, label: 'ROI Achieved', value: '450%', description: 'Within first year' },
      ],
      quote: "The automation capabilities exceeded our expectations. We're now processing transactions in real-time across multiple currencies seamlessly.",
      author: "Michael Chen",
      role: "Finance Director, Global Manufacturing Inc",
      image: '/api/placeholder/400/300'
    },
    {
      company: 'FinanceFlow Partners',
      industry: 'Financial Services',
      challenge: 'Regulatory compliance reporting bottlenecks',
      solution: 'Automated compliance reporting with audit trails',
      results: [
        { icon: Clock, label: 'Report Generation', value: '24/7', description: 'Automated real-time reports' },
        { icon: TrendingUp, label: 'Compliance Score', value: '100%', description: 'Perfect audit results' },
        { icon: DollarSign, label: 'Risk Reduction', value: '90%', description: 'Compliance risk mitigation' },
      ],
      quote: "MizuFlow's compliance automation gave us confidence in our reporting and freed up our team to focus on strategic initiatives.",
      author: "Lisa Rodriguez",
      role: "Head of Compliance, FinanceFlow Partners",
      image: '/api/placeholder/400/300'
    }
  ];

  const nextCase = () => {
    setCurrentCase((prev) => (prev + 1) % caseStudies.length);
  };

  const prevCase = () => {
    setCurrentCase((prev) => (prev - 1 + caseStudies.length) % caseStudies.length);
  };

  return (
    <section className="py-24 bg-ocean-gradient-light">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Success <span className="text-ocean-gradient">Stories</span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            See how leading companies have transformed their financial operations with MizuFlow's 
            intelligent automation platform.
          </p>
        </motion.div>

        {/* Case Study Carousel */}
        <div className="relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentCase}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
            >
              <Card className="bg-white shadow-xl border-border/20 overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid lg:grid-cols-2">
                    {/* Content */}
                    <div className="p-8 lg:p-12 space-y-8">
                      {/* Company Info */}
                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-3 h-3 bg-primary rounded-full"></div>
                          <span className="text-primary font-medium text-sm">
                            {caseStudies[currentCase].industry}
                          </span>
                        </div>
                        <h3 className="text-2xl font-bold text-foreground">
                          {caseStudies[currentCase].company}
                        </h3>
                      </div>

                      {/* Challenge & Solution */}
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-foreground mb-2">Challenge:</h4>
                          <p className="text-muted-foreground">
                            {caseStudies[currentCase].challenge}
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-foreground mb-2">Solution:</h4>
                          <p className="text-muted-foreground">
                            {caseStudies[currentCase].solution}
                          </p>
                        </div>
                      </div>

                      {/* Results */}
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        {caseStudies[currentCase].results.map((result, index) => (
                          <div key={index} className="text-center p-4 bg-ocean-gradient-light rounded-lg">
                            <result.icon className="w-6 h-6 text-primary mx-auto mb-2" />
                            <div className="text-xl font-bold text-primary mb-1">
                              {result.value}
                            </div>
                            <div className="text-xs font-medium text-foreground mb-1">
                              {result.label}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {result.description}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Quote */}
                      <div className="bg-muted/30 p-6 rounded-lg">
                        <blockquote className="text-foreground italic mb-4">
                          "{caseStudies[currentCase].quote}"
                        </blockquote>
                        <div>
                          <div className="font-semibold text-foreground">
                            {caseStudies[currentCase].author}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {caseStudies[currentCase].role}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Visual */}
                    <div className="bg-ocean-gradient-light flex items-center justify-center p-8">
                      <div className="w-full max-w-sm">
                        {/* Placeholder for case study visual */}
                        <div className="aspect-square bg-white/50 rounded-2xl flex items-center justify-center">
                          <TrendingUp className="w-24 h-24 text-primary" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8">
            <Button
              onClick={prevCase}
              variant="outline"
              size="sm"
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </Button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {caseStudies.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentCase(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentCase ? 'bg-primary' : 'bg-muted'
                  }`}
                  aria-label={`Go to case study ${index + 1}`}
                />
              ))}
            </div>

            <Button
              onClick={nextCase}
              variant="outline"
              size="sm"
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              Next
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-16"
        >
          <p className="text-muted-foreground mb-6">
            Ready to write your own success story?
          </p>
          <Button
            size="lg"
            asChild
            className="bg-ocean-gradient hover:opacity-90 text-white"
          >
            <a href="#contact">Start Your Transformation</a>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default CaseStudies;
