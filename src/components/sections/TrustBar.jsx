'use client';

import { motion } from 'framer-motion';

const TrustBar = () => {
  // Placeholder company logos - in production, these would be actual client logos
  const companies = [
    { name: 'TechCorp', logo: 'TC' },
    { name: 'FinanceFlow', logo: 'FF' },
    { name: 'DataDrive', logo: 'DD' },
    { name: 'CloudSync', logo: 'CS' },
    { name: 'AutoMate', logo: 'AM' },
    { name: 'SmartBooks', logo: 'SB' },
    { name: 'ProAnalytics', logo: 'PA' },
    { name: 'FlowTech', logo: 'FT' },
  ];

  // Duplicate the array for seamless infinite scroll
  const duplicatedCompanies = [...companies, ...companies];

  return (
    <section className="py-16 bg-background border-y border-border/50">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <p className="text-muted-foreground font-medium">
            Trusted by leading companies worldwide
          </p>
        </motion.div>

        {/* Scrolling Container */}
        <div className="relative overflow-hidden">
          {/* Gradient Overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-background to-transparent z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-background to-transparent z-10"></div>

          {/* Scrolling Content */}
          <motion.div
            className="flex space-x-12"
            animate={{
              x: [0, -50 * companies.length + '%'],
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: 'loop',
                duration: 20,
                ease: 'linear',
              },
            }}
          >
            {duplicatedCompanies.map((company, index) => (
              <div
                key={`${company.name}-${index}`}
                className="flex-shrink-0 flex items-center justify-center"
              >
                <div className="group cursor-pointer">
                  {/* Logo Placeholder */}
                  <div className="w-24 h-16 bg-muted/50 rounded-lg flex items-center justify-center group-hover:bg-primary/10 transition-all duration-300 border border-border/30 group-hover:border-primary/30">
                    <span className="text-lg font-bold text-muted-foreground group-hover:text-primary transition-colors">
                      {company.logo}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Stats Row */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          <div>
            <div className="text-2xl md:text-3xl font-bold text-primary mb-2">500+</div>
            <div className="text-sm text-muted-foreground">Companies Served</div>
          </div>
          <div>
            <div className="text-2xl md:text-3xl font-bold text-primary mb-2">$2.4B+</div>
            <div className="text-sm text-muted-foreground">Transactions Processed</div>
          </div>
          <div>
            <div className="text-2xl md:text-3xl font-bold text-primary mb-2">99.9%</div>
            <div className="text-sm text-muted-foreground">Uptime Guarantee</div>
          </div>
          <div>
            <div className="text-2xl md:text-3xl font-bold text-primary mb-2">24/7</div>
            <div className="text-sm text-muted-foreground">Support Available</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TrustBar;
