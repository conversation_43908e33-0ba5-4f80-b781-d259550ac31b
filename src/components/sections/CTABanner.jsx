'use client';

import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Zap, Shield, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

const CTABanner = () => {
  const features = [
    { icon: Zap, text: 'Quick Setup' },
    { icon: Shield, text: 'Enterprise Security' },
    { icon: TrendingUp, text: 'Proven ROI' },
  ];

  return (
    <section className="py-24 bg-ocean-gradient text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 border border-white/20 rounded-full"></div>
        <div className="absolute top-20 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-10 right-10 w-28 h-28 border border-white/20 rounded-full"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 border border-white/20 text-white text-sm font-medium">
              <Zap className="w-4 h-4 mr-2" />
              Transform Your Business Today
            </div>

            {/* Headline */}
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
              Ready to Revolutionize Your{' '}
              <span className="text-white/90">Financial Operations?</span>
            </h2>

            {/* Description */}
            <p className="text-lg md:text-xl text-white/80 leading-relaxed max-w-3xl mx-auto">
              Join hundreds of companies that have already transformed their financial processes 
              with MizuFlow's intelligent automation platform. Start your journey today.
            </p>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-6 py-6"
            >
              {features.map((feature, index) => (
                <div key={feature.text} className="flex items-center space-x-2 text-white/90">
                  <feature.icon className="w-5 h-5" />
                  <span className="font-medium">{feature.text}</span>
                </div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                size="lg"
                asChild
                className="bg-white text-primary hover:bg-white/90 shadow-xl group px-8"
              >
                <a href="#contact" className="flex items-center">
                  Get Started Now
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </a>
              </Button>
              <Button
                size="lg"
                variant="outline"
                asChild
                className="border-white/30 text-white hover:bg-white/10 hover:border-white px-8"
              >
                <a href="https://demo.mizuflow.ai" target="_blank" rel="noopener noreferrer">
                  Watch Demo
                </a>
              </Button>
            </motion.div>

            {/* Trust Indicator */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="pt-8"
            >
              <p className="text-white/60 text-sm">
                ✓ No setup fees • ✓ 30-day free trial • ✓ Cancel anytime
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Floating Elements */}
      <motion.div
        animate={{
          y: [-10, 10, -10],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute top-20 left-10 w-20 h-20 bg-white/5 rounded-2xl backdrop-blur-sm hidden lg:block"
      />
      <motion.div
        animate={{
          y: [10, -10, 10],
          rotate: [0, -5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute bottom-20 right-10 w-16 h-16 bg-white/5 rounded-2xl backdrop-blur-sm hidden lg:block"
      />
    </section>
  );
};

export default CTABanner;
