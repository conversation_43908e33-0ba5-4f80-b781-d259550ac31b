'use client';

import { motion } from 'framer-motion';
import { Check, ArrowRight, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const PricingTeaser = () => {
  const plans = [
    {
      name: 'Starter',
      description: 'Perfect for small businesses getting started with automation',
      price: 'Custom',
      period: 'per month',
      features: [
        'Basic invoice automation',
        'Up to 100 transactions/month',
        'Email support',
        'Standard integrations',
        'Basic reporting'
      ],
      popular: false,
      cta: 'Get Started'
    },
    {
      name: 'Professional',
      description: 'Ideal for growing companies with complex financial needs',
      price: 'Custom',
      period: 'per month',
      features: [
        'Full automation suite',
        'Unlimited transactions',
        'Priority support',
        'Advanced integrations',
        'AI-powered insights',
        'Custom workflows',
        'Dedicated account manager'
      ],
      popular: true,
      cta: 'Most Popular'
    },
    {
      name: 'Enterprise',
      description: 'Comprehensive solution for large organizations',
      price: 'Custom',
      period: 'per month',
      features: [
        'Everything in Professional',
        'White-label options',
        'Custom development',
        'SLA guarantees',
        'On-premise deployment',
        'Advanced security',
        'Training & onboarding'
      ],
      popular: false,
      cta: 'Contact Sales'
    }
  ];

  return (
    <section className="py-24 bg-background">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Simple, <span className="text-ocean-gradient">Transparent Pricing</span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Choose the perfect plan for your business. All plans include our core automation 
            features with scalable pricing based on your needs.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`relative ${plan.popular ? 'lg:scale-105' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-ocean-gradient text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 mr-1" />
                    Most Popular
                  </div>
                </div>
              )}
              
              <Card className={`h-full ${
                plan.popular 
                  ? 'border-primary shadow-xl bg-ocean-gradient-light' 
                  : 'border-border/50 hover:border-primary/30'
              } transition-all duration-300 hover:shadow-lg`}>
                <CardContent className="p-8">
                  <div className="space-y-6">
                    {/* Plan Header */}
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-foreground mb-2">{plan.name}</h3>
                      <p className="text-muted-foreground text-sm mb-4">{plan.description}</p>
                      <div className="space-y-1">
                        <div className="text-3xl font-bold text-primary">{plan.price}</div>
                        <div className="text-muted-foreground text-sm">{plan.period}</div>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <Check className="w-5 h-5 text-primary flex-shrink-0" />
                          <span className="text-sm text-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={`w-full ${
                        plan.popular
                          ? 'bg-ocean-gradient hover:opacity-90 text-white'
                          : 'border-primary text-primary hover:bg-primary hover:text-white'
                      } group`}
                      variant={plan.popular ? 'default' : 'outline'}
                    >
                      {plan.cta}
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <Card className="bg-ocean-gradient-light border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Need a Custom Solution?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Every business is unique. Let's discuss your specific requirements and create 
                a tailored automation solution that fits your exact needs and budget.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  asChild
                  className="bg-ocean-gradient hover:opacity-90 text-white"
                >
                  <a href="#contact">Schedule Consultation</a>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  asChild
                  className="border-primary text-primary hover:bg-primary hover:text-white"
                >
                  <a href="mailto:<EMAIL>">Contact Sales</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingTeaser;
