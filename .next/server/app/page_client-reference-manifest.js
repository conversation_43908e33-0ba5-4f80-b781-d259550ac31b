globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/Footer.jsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navbar.jsx":{"*":{"id":"(ssr)/./src/components/layout/Navbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/About.jsx":{"*":{"id":"(ssr)/./src/components/sections/About.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Benefits.jsx":{"*":{"id":"(ssr)/./src/components/sections/Benefits.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/CaseStudies.jsx":{"*":{"id":"(ssr)/./src/components/sections/CaseStudies.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Contact.jsx":{"*":{"id":"(ssr)/./src/components/sections/Contact.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/CTABanner.jsx":{"*":{"id":"(ssr)/./src/components/sections/CTABanner.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.jsx":{"*":{"id":"(ssr)/./src/components/sections/Hero.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/PricingTeaser.jsx":{"*":{"id":"(ssr)/./src/components/sections/PricingTeaser.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Services.jsx":{"*":{"id":"(ssr)/./src/components/sections/Services.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/TrustBar.jsx":{"*":{"id":"(ssr)/./src/components/sections/TrustBar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\",\"display\":\"swap\"}],\"variableName\":\"roboto\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\",\"display\":\"swap\"}],\"variableName\":\"roboto\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/layout/Footer.jsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/layout/Navbar.jsx":{"id":"(app-pages-browser)/./src/components/layout/Navbar.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/About.jsx":{"id":"(app-pages-browser)/./src/components/sections/About.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Benefits.jsx":{"id":"(app-pages-browser)/./src/components/sections/Benefits.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/CaseStudies.jsx":{"id":"(app-pages-browser)/./src/components/sections/CaseStudies.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Contact.jsx":{"id":"(app-pages-browser)/./src/components/sections/Contact.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/CTABanner.jsx":{"id":"(app-pages-browser)/./src/components/sections/CTABanner.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Hero.jsx":{"id":"(app-pages-browser)/./src/components/sections/Hero.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/PricingTeaser.jsx":{"id":"(app-pages-browser)/./src/components/sections/PricingTeaser.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Services.jsx":{"id":"(app-pages-browser)/./src/components/sections/Services.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/TrustBar.jsx":{"id":"(app-pages-browser)/./src/components/sections/TrustBar.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/":[],"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.jsx":{"*":{"id":"(rsc)/./src/components/layout/Footer.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navbar.jsx":{"*":{"id":"(rsc)/./src/components/layout/Navbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/About.jsx":{"*":{"id":"(rsc)/./src/components/sections/About.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Benefits.jsx":{"*":{"id":"(rsc)/./src/components/sections/Benefits.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/CaseStudies.jsx":{"*":{"id":"(rsc)/./src/components/sections/CaseStudies.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Contact.jsx":{"*":{"id":"(rsc)/./src/components/sections/Contact.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/CTABanner.jsx":{"*":{"id":"(rsc)/./src/components/sections/CTABanner.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.jsx":{"*":{"id":"(rsc)/./src/components/sections/Hero.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/PricingTeaser.jsx":{"*":{"id":"(rsc)/./src/components/sections/PricingTeaser.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Services.jsx":{"*":{"id":"(rsc)/./src/components/sections/Services.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/TrustBar.jsx":{"*":{"id":"(rsc)/./src/components/sections/TrustBar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}